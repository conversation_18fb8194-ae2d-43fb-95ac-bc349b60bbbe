/// ============================================================================
/// AUTH STATE MANAGER - Forever Plan Architecture
/// ============================================================================
///
/// مدير حالة المصادقة - بنية الخطة الدائمة
/// Centralized authentication state coordination with proper lifecycle handling
///
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ✅ Manages authentication state transitions and lifecycle
/// ✅ Coordinates between different auth providers (Google, Email)
/// ✅ Comprehensive error handling and recovery
/// ✅ Production-ready state management with Riverpod
/// ============================================================================
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'auth_models.dart';
import 'auth_interfaces.dart';
import 'token_validation_service.dart';

import 'google_auth_service.dart';
import 'enhanced_secure_token_storage.dart';
import '../networking/simple_api_client.dart';

part 'auth_state_manager.freezed.dart';
part 'auth_state_manager.g.dart';

// =============================================================================
// AUTH STATE MANAGER MODELS
// =============================================================================

/// Authentication lifecycle states for internal state management
enum AuthLifecycleState {
  /// Not yet initialized
  notInitialized,

  /// Currently initializing
  initializing,

  /// Validating stored tokens
  validatingTokens,

  /// Restoring user session
  restoringSession,

  /// Ready for normal operations
  ready,

  /// Authentication in progress
  authenticating,

  /// Refreshing tokens
  refreshingTokens,

  /// Signing out
  signingOut,

  /// Error state requiring intervention
  error,

  /// Degraded state (offline mode)
  degraded,
}

/// State transition context for debugging and monitoring
@freezed
abstract class StateTransitionContext with _$StateTransitionContext {
  const factory StateTransitionContext({
    required AuthLifecycleState fromState,
    required AuthLifecycleState toState,
    required DateTime timestamp,
    String? reason,
    Map<String, dynamic>? metadata,
    String? triggeredBy,
  }) = _StateTransitionContext;
}

/// Auth provider coordination result
@freezed
abstract class AuthProviderResult with _$AuthProviderResult {
  /// Successful operation
  const factory AuthProviderResult.success({
    required AuthProvider provider,
    required AuthResult result,
    Map<String, dynamic>? metadata,
  }) = AuthProviderSuccess;

  /// Failed operation
  const factory AuthProviderResult.failure({
    required AuthProvider provider,
    required String error,
    required AuthErrorType errorType,
    @Default(true) bool isRecoverable,
    Map<String, dynamic>? details,
  }) = AuthProviderFailure;

  /// Operation cancelled by user
  const factory AuthProviderResult.cancelled({
    required AuthProvider provider,
    String? reason,
  }) = AuthProviderCancelled;
}

/// Auth state manager configuration
@freezed
abstract class AuthStateManagerConfig with _$AuthStateManagerConfig {
  const factory AuthStateManagerConfig({
    /// Maximum time to wait for initialization
    @Default(Duration(seconds: 10)) Duration initializationTimeout,

    /// Token validation interval
    @Default(Duration(minutes: 5)) Duration tokenValidationInterval,

    /// Session refresh threshold (refresh when token expires in this time)
    @Default(Duration(minutes: 10)) Duration sessionRefreshThreshold,

    /// Maximum retry attempts for failed operations
    @Default(3) int maxRetryAttempts,

    /// Enable automatic token refresh
    @Default(true) bool enableAutoRefresh,

    /// Enable state transition logging
    @Default(true) bool enableStateLogging,
  }) = _AuthStateManagerConfig;
}

// =============================================================================
// AUTH STATE MANAGER
// =============================================================================

/// Enhanced authentication state manager with centralized coordination
/// Manages authentication state transitions and provider coordination
@riverpod
class AuthStateManager extends _$AuthStateManager {
  // State management
  AuthLifecycleState _lifecycleState = AuthLifecycleState.notInitialized;
  AuthState _currentAuthState = const AuthState.initial();
  User? _currentUser;

  // Configuration
  late final AuthStateManagerConfig _config;

  // Dependencies
  late final TokenValidationService _tokenValidationService;
  late final GoogleAuthService _googleAuthService;
  late final ITokenStorage _tokenStorage;
  late final SimpleApiClient _apiClient;

  // State transition tracking
  final List<StateTransitionContext> _stateHistory = [];
  Timer? _tokenValidationTimer;
  Timer? _sessionRefreshTimer;

  // Coordination state
  final Map<AuthProvider, bool> _providerAvailability = {};
  AuthProvider? _lastUsedProvider;

  @override
  AuthStateManager build() {
    // Initialize configuration
    _config = const AuthStateManagerConfig();

    // Get dependencies
    _tokenValidationService = ref.read(tokenValidationServiceProvider);
    _googleAuthService = ref.read(googleAuthServiceProvider);
    _tokenStorage = ref.read(enhancedSecureTokenStorageProvider);
    _apiClient = ref.read(simpleApiClientProvider);

    // Initialize provider availability
    _providerAvailability[AuthProvider.email] = true;
    _providerAvailability[AuthProvider.google] = true;

    return this;
  }

  // ---------------------------------------------------------------------------
  // PUBLIC API - STATE ACCESS
  // ---------------------------------------------------------------------------

  /// Get current authentication state
  AuthState get currentAuthState => _currentAuthState;

  /// Get current authenticated user
  User? get currentUser => _currentUser;

  /// Get current lifecycle state
  AuthLifecycleState get lifecycleState => _lifecycleState;

  /// Check if authentication system is ready
  bool get isReady => _lifecycleState == AuthLifecycleState.ready;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentAuthState.maybeWhen(
    authenticated: (user, token, refreshToken, tokenExpiry, lastLoginAt) =>
        true,
    orElse: () => false,
  );

  /// Get state transition history for debugging
  List<StateTransitionContext> get stateHistory =>
      List.unmodifiable(_stateHistory);

  /// Get provider availability status
  Map<AuthProvider, bool> get providerAvailability =>
      Map.unmodifiable(_providerAvailability);

  /// Get last used authentication provider
  AuthProvider? get lastUsedProvider => _lastUsedProvider;

  // ---------------------------------------------------------------------------
  // PUBLIC API - LIFECYCLE MANAGEMENT
  // ---------------------------------------------------------------------------

  /// Initialize authentication state manager
  /// Returns true if initialization was successful
  Future<bool> initialize() async {
    if (_lifecycleState != AuthLifecycleState.notInitialized) {
      debugPrint('⚠️ AuthStateManager - Already initialized or initializing');
      return _lifecycleState == AuthLifecycleState.ready;
    }

    try {
      _transitionToState(
        AuthLifecycleState.initializing,
        'initialize() called',
      );

      // Initialize with timeout
      final initializationFuture = _performInitialization();
      final result = await initializationFuture.timeout(
        _config.initializationTimeout,
        onTimeout: () {
          debugPrint('🔴 AuthStateManager - Initialization timeout');
          _transitionToState(
            AuthLifecycleState.error,
            'Initialization timeout',
          );
          return false;
        },
      );

      if (result) {
        _transitionToState(
          AuthLifecycleState.ready,
          'Initialization completed successfully',
        );
        _startPeriodicValidation();
        return true;
      } else {
        _transitionToState(AuthLifecycleState.error, 'Initialization failed');
        return false;
      }
    } catch (error, stackTrace) {
      debugPrint('🔴 AuthStateManager - Initialization error: $error');
      debugPrint('Stack trace: $stackTrace');

      _transitionToState(
        AuthLifecycleState.error,
        'Initialization exception: $error',
      );
      _updateAuthState(
        AuthState.error(
          message: 'Initialization failed: $error',
          errorType: AuthErrorType.unknown,
          isRecoverable: true,
        ),
      );

      return false;
    }
  }

  /// Dispose resources and cleanup
  void dispose() {
    debugPrint('🧹 AuthStateManager - Disposing resources');

    _tokenValidationTimer?.cancel();
    _sessionRefreshTimer?.cancel();

    _transitionToState(AuthLifecycleState.notInitialized, 'dispose() called');
  }

  /// Reset authentication state manager to initial state
  Future<void> reset() async {
    debugPrint('🔄 AuthStateManager - Resetting state manager');

    dispose();

    _currentAuthState = const AuthState.initial();
    _currentUser = null;
    _stateHistory.clear();
    _lastUsedProvider = null;

    // Reset provider availability
    _providerAvailability[AuthProvider.email] = true;
    _providerAvailability[AuthProvider.google] = true;
  }

  // ---------------------------------------------------------------------------
  // PUBLIC API - AUTHENTICATION OPERATIONS
  // ---------------------------------------------------------------------------

  /// Sign in with email and password
  Future<AuthProviderResult> signInWithEmail(
    String email,
    String password,
  ) async {
    if (!_ensureReady()) {
      return const AuthProviderResult.failure(
        provider: AuthProvider.email,
        error: 'Authentication system not ready',
        errorType: AuthErrorType.serviceUnavailable,
        isRecoverable: true,
      );
    }

    try {
      _transitionToState(
        AuthLifecycleState.authenticating,
        'Email sign-in started',
      );
      _updateAuthState(const AuthState.loading());

      // EmailAuthService is for validation only - use API client for actual authentication
      final response = await _apiClient.post(
        '/api/v1/auth/login',
        data: {'email': email, 'password': password},
      );

      if (response.isSuccess && response.data != null) {
        final authData = response.data as Map<String, dynamic>;
        final user = User.fromJson(authData['user']);
        final token = authData['access_token'] as String;
        final refreshToken = authData['refresh_token'] as String?;
        final expiresIn = authData['expires_in'] as int? ?? 900;
        final tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn));

        final result = AuthResult.success(
          user: user,
          token: token,
          refreshToken: refreshToken,
          tokenExpiry: tokenExpiry,
        );

        _handleSuccessfulAuthentication(user, token, refreshToken, tokenExpiry);
        _lastUsedProvider = AuthProvider.email;

        return AuthProviderResult.success(
          provider: AuthProvider.email,
          result: result,
        );
      } else {
        final error = response.message ?? 'Email authentication failed';
        return AuthProviderResult.failure(
          provider: AuthProvider.email,
          error: error,
          errorType: AuthErrorType.invalidCredentials,
        );
      }
    } catch (error, stackTrace) {
      debugPrint('🔴 AuthStateManager - Email sign-in error: $error');
      _handleAuthenticationFailure(
        error.toString(),
        AuthErrorType.unknown,
        AuthProvider.email,
      );

      return AuthProviderResult.failure(
        provider: AuthProvider.email,
        error: 'Sign-in failed: $error',
        errorType: AuthErrorType.unknown,
        isRecoverable: true,
        details: {'stackTrace': stackTrace.toString()},
      );
    }
  }

  /// Sign in with Google OAuth
  Future<AuthProviderResult> signInWithGoogle() async {
    if (!_ensureReady()) {
      return const AuthProviderResult.failure(
        provider: AuthProvider.google,
        error: 'Authentication system not ready',
        errorType: AuthErrorType.serviceUnavailable,
        isRecoverable: true,
      );
    }

    if (!(_providerAvailability[AuthProvider.google] ?? false)) {
      return const AuthProviderResult.failure(
        provider: AuthProvider.google,
        error: 'Google authentication is not available',
        errorType: AuthErrorType.serviceUnavailable,
        isRecoverable: false,
      );
    }

    try {
      _transitionToState(
        AuthLifecycleState.authenticating,
        'Google sign-in started',
      );
      _updateAuthState(const AuthState.loading());

      final result = await _googleAuthService.authenticate();

      return result.when(
        success: (idToken, accessToken, userInfo, expiryDate) async {
          // Exchange Google token with backend
          final authResult = await _exchangeGoogleToken(idToken);

          return authResult.when(
            success: (user, token, refreshToken, tokenExpiry, metadata) {
              _handleSuccessfulAuthentication(
                user,
                token,
                refreshToken,
                tokenExpiry,
              );
              _lastUsedProvider = AuthProvider.google;

              return AuthProviderResult.success(
                provider: AuthProvider.google,
                result: authResult,
                metadata: {
                  'googleAccessToken': accessToken,
                  'googleUser': {
                    'id': userInfo.id,
                    'email': userInfo.email,
                    'displayName': userInfo.displayName,
                    'photoUrl': userInfo.photoUrl,
                  },
                  ...metadata ?? {},
                },
              );
            },
            failure: (error, errorCode, errorType, isRecoverable, details) {
              _handleAuthenticationFailure(
                error,
                errorType,
                AuthProvider.google,
              );

              return AuthProviderResult.failure(
                provider: AuthProvider.google,
                error: error,
                errorType: errorType,
                isRecoverable: isRecoverable,
                details: {'errorCode': errorCode},
              );
            },
            cancelled: (reason) {
              _transitionToState(
                AuthLifecycleState.ready,
                'Google sign-in cancelled',
              );
              _updateAuthState(const AuthState.unauthenticated());

              return AuthProviderResult.cancelled(
                provider: AuthProvider.google,
                reason: reason,
              );
            },
            pending: (message, pendingAction, actionData) {
              _updateAuthState(
                AuthState.emailVerificationPending(
                  email: userInfo.email,
                  sentAt: DateTime.now(),
                ),
              );

              return AuthProviderResult.success(
                provider: AuthProvider.google,
                result: authResult,
              );
            },
          );
        },
        failure: (error, errorCode, details) {
          _handleAuthenticationFailure(
            error,
            AuthErrorType.oauthError,
            AuthProvider.google,
          );

          return AuthProviderResult.failure(
            provider: AuthProvider.google,
            error: error,
            errorType: AuthErrorType.oauthError,
            isRecoverable: true,
            details: {'errorCode': errorCode},
          );
        },
        cancelled: (reason) {
          _transitionToState(
            AuthLifecycleState.ready,
            'Google sign-in cancelled by user',
          );
          _updateAuthState(const AuthState.unauthenticated());

          return AuthProviderResult.cancelled(
            provider: AuthProvider.google,
            reason: reason,
          );
        },
      );
    } catch (error, stackTrace) {
      debugPrint('🔴 AuthStateManager - Google sign-in error: $error');
      _handleAuthenticationFailure(
        error.toString(),
        AuthErrorType.oauthError,
        AuthProvider.google,
      );

      return AuthProviderResult.failure(
        provider: AuthProvider.google,
        error: 'Google sign-in failed: $error',
        errorType: AuthErrorType.oauthError,
        isRecoverable: true,
        details: {'stackTrace': stackTrace.toString()},
      );
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    if (_lifecycleState == AuthLifecycleState.signingOut) {
      debugPrint('⚠️ AuthStateManager - Sign-out already in progress');
      return;
    }

    try {
      _transitionToState(AuthLifecycleState.signingOut, 'Sign-out started');
      _updateAuthState(const AuthState.loading());

      // Clear tokens from storage
      await _tokenStorage.clearAllData();

      // Sign out from providers
      if (_lastUsedProvider == AuthProvider.google) {
        await _googleAuthService.signOut();
      }

      // Clear current state
      _currentUser = null;
      _lastUsedProvider = null;

      _transitionToState(AuthLifecycleState.ready, 'Sign-out completed');
      _updateAuthState(const AuthState.unauthenticated());

      debugPrint('✅ AuthStateManager - User signed out successfully');
    } catch (error, stackTrace) {
      debugPrint('🔴 AuthStateManager - Sign-out error: $error');
      debugPrint('Stack trace: $stackTrace');

      // Even if sign-out fails, clear local state
      _currentUser = null;
      _lastUsedProvider = null;

      _transitionToState(
        AuthLifecycleState.ready,
        'Sign-out completed with errors',
      );
      _updateAuthState(const AuthState.unauthenticated());
    }
  }

  /// Refresh current authentication token
  Future<bool> refreshToken() async {
    if (!isAuthenticated) {
      debugPrint(
        '⚠️ AuthStateManager - Cannot refresh token - not authenticated',
      );
      return false;
    }

    try {
      _transitionToState(
        AuthLifecycleState.refreshingTokens,
        'Token refresh started',
      );

      final result = await _tokenValidationService.refreshToken();

      return result.when(
        success: (isValid, token, expiryDate, metadata) {
          _transitionToState(
            AuthLifecycleState.ready,
            'Token refresh completed',
          );
          return true;
        },
        failure: (error, errorType, isRecoverable, retryAfterSeconds, details) {
          debugPrint('🔴 AuthStateManager - Token refresh failed: $error');

          if (!isRecoverable) {
            // Force sign-out if token refresh is not recoverable
            _handleSessionExpired();
          } else {
            _transitionToState(
              AuthLifecycleState.ready,
              'Token refresh failed but recoverable',
            );
          }

          return false;
        },
        circuitOpen: (reason, retryAfter, failureCount) {
          debugPrint(
            '⚠️ AuthStateManager - Token refresh circuit breaker open: $reason',
          );
          _transitionToState(
            AuthLifecycleState.degraded,
            'Token refresh circuit breaker open',
          );
          return false;
        },
      );
    } catch (error, stackTrace) {
      debugPrint('🔴 AuthStateManager - Token refresh error: $error');
      debugPrint('🔴 AuthStateManager - Stack trace: $stackTrace');
      _transitionToState(
        AuthLifecycleState.ready,
        'Token refresh failed with exception',
      );
      return false;
    }
  }

  // ---------------------------------------------------------------------------
  // PRIVATE METHODS - INITIALIZATION
  // ---------------------------------------------------------------------------

  /// Perform the actual initialization process
  Future<bool> _performInitialization() async {
    try {
      debugPrint('🔄 AuthStateManager - Starting initialization');

      // Step 1: Validate stored tokens
      _transitionToState(
        AuthLifecycleState.validatingTokens,
        'Validating stored tokens',
      );

      final tokenResult = await _tokenValidationService.validateStoredToken();

      final isTokenValid = tokenResult.when(
        success: (isValid, token, expiryDate, metadata) => isValid,
        failure:
            (error, errorType, isRecoverable, retryAfterSeconds, details) =>
                false,
        circuitOpen: (reason, retryAfter, failureCount) => false,
      );

      if (isTokenValid) {
        // Step 2: Restore user session
        _transitionToState(
          AuthLifecycleState.restoringSession,
          'Restoring user session',
        );

        final sessionData = await _tokenStorage.getSessionData();
        if (sessionData.isNotEmpty && sessionData['user'] != null) {
          final user = User.fromJson(sessionData['user']);
          _currentUser = user;
          _lastUsedProvider = user.authProvider;

          _updateAuthState(
            AuthState.authenticated(
              user: user,
              token: await _tokenStorage.getToken() ?? '',
            ),
          );
          debugPrint(
            '✅ AuthStateManager - Session restored for user: ${user.email}',
          );
        } else {
          _updateAuthState(const AuthState.unauthenticated());
          debugPrint('ℹ️ AuthStateManager - No valid session found');
        }
      } else {
        _updateAuthState(const AuthState.unauthenticated());
        debugPrint('ℹ️ AuthStateManager - No valid token found');
      }

      return true;
    } catch (error, stackTrace) {
      debugPrint('🔴 AuthStateManager - Initialization error: $error');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  /// Start periodic token validation
  void _startPeriodicValidation() {
    if (!_config.enableAutoRefresh) return;

    _tokenValidationTimer?.cancel();
    _tokenValidationTimer = Timer.periodic(_config.tokenValidationInterval, (
      _,
    ) {
      _performPeriodicValidation();
    });

    debugPrint('✅ AuthStateManager - Started periodic token validation');
  }

  /// Perform periodic token validation
  Future<void> _performPeriodicValidation() async {
    if (!isAuthenticated) return;

    try {
      final tokenExpiry = await _tokenStorage.getTokenExpiry();
      if (tokenExpiry != null) {
        final timeUntilExpiry = tokenExpiry.difference(DateTime.now());

        if (timeUntilExpiry <= _config.sessionRefreshThreshold) {
          debugPrint('🔄 AuthStateManager - Token expiring soon, refreshing');
          await refreshToken();
        }
      }
    } catch (error) {
      debugPrint('🔴 AuthStateManager - Periodic validation error: $error');
    }
  }

  // ---------------------------------------------------------------------------
  // PRIVATE METHODS - AUTHENTICATION HANDLING
  // ---------------------------------------------------------------------------

  /// Handle successful authentication
  void _handleSuccessfulAuthentication(
    User user,
    String token,
    String? refreshToken,
    DateTime? tokenExpiry,
  ) async {
    try {
      // Store tokens
      await _tokenStorage.storeToken(token, expiryDate: tokenExpiry);
      if (refreshToken != null) {
        await _tokenStorage.storeRefreshToken(refreshToken);
      }

      // Store session data
      await _tokenStorage.storeSessionData({
        'user': user.toJson(),
        'lastLogin': DateTime.now().toIso8601String(),
        'provider': user.authProvider.name,
      });

      // Update state
      _currentUser = user;
      _transitionToState(AuthLifecycleState.ready, 'Authentication successful');
      _updateAuthState(AuthState.authenticated(user: user, token: token));

      debugPrint(
        '✅ AuthStateManager - Authentication successful for: ${user.email}',
      );
    } catch (error) {
      debugPrint('🔴 AuthStateManager - Error storing auth data: $error');
    }
  }

  /// Handle authentication failure
  void _handleAuthenticationFailure(
    String error,
    AuthErrorType errorType,
    AuthProvider provider,
  ) {
    _transitionToState(AuthLifecycleState.ready, 'Authentication failed');
    _updateAuthState(
      AuthState.error(
        message: error,
        errorType: errorType,
        isRecoverable: errorType != AuthErrorType.invalidCredentials,
      ),
    );

    // Update provider availability if needed
    if (errorType == AuthErrorType.serviceUnavailable) {
      _providerAvailability[provider] = false;
    }
  }

  /// Handle session expiration
  void _handleSessionExpired() {
    debugPrint('⚠️ AuthStateManager - Session expired, signing out');

    _currentUser = null;
    _lastUsedProvider = null;

    _transitionToState(AuthLifecycleState.ready, 'Session expired');
    _updateAuthState(const AuthState.sessionExpired());

    // Clear stored tokens
    _tokenStorage.clearAllData().catchError((error) {
      debugPrint('🔴 AuthStateManager - Error clearing expired tokens: $error');
    });
  }

  /// Exchange Google token with backend
  Future<AuthResult> _exchangeGoogleToken(String idToken) async {
    try {
      final response = await _apiClient.post(
        '/api/v1/auth/google',
        data: {'id_token': idToken},
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final authData = AuthData.fromJson(data);

        return AuthResult.success(
          user: authData.user,
          token: authData.accessToken,
          refreshToken: authData.refreshToken,
          tokenExpiry: DateTime.now().add(
            Duration(seconds: authData.expiresIn),
          ),
        );
      } else {
        return AuthResult.failure(
          error: 'Google authentication failed',
          errorType: AuthErrorType.oauthError,
        );
      }
    } catch (error) {
      return AuthResult.failure(
        error: 'Google token exchange failed: $error',
        errorType: AuthErrorType.network,
      );
    }
  }

  // ---------------------------------------------------------------------------
  // PRIVATE METHODS - STATE MANAGEMENT
  // ---------------------------------------------------------------------------

  /// Transition to new lifecycle state
  void _transitionToState(AuthLifecycleState newState, String reason) {
    if (_lifecycleState == newState) return;

    final transition = StateTransitionContext(
      fromState: _lifecycleState,
      toState: newState,
      timestamp: DateTime.now(),
      reason: reason,
    );

    _stateHistory.add(transition);

    // Keep only last 50 transitions
    if (_stateHistory.length > 50) {
      _stateHistory.removeAt(0);
    }

    if (_config.enableStateLogging) {
      debugPrint(
        '🔄 AuthStateManager - State: ${_lifecycleState.name} → ${newState.name} ($reason)',
      );
    }

    _lifecycleState = newState;
  }

  /// Update authentication state
  void _updateAuthState(AuthState newState) {
    if (_currentAuthState != newState) {
      _currentAuthState = newState;

      if (_config.enableStateLogging) {
        debugPrint(
          '🔄 AuthStateManager - Auth state updated: ${newState.runtimeType}',
        );
      }
    }
  }

  /// Ensure the auth state manager is ready for operations
  bool _ensureReady() {
    if (_lifecycleState != AuthLifecycleState.ready) {
      debugPrint(
        '⚠️ AuthStateManager - Operation attempted but not ready (state: ${_lifecycleState.name})',
      );
      return false;
    }
    return true;
  }
}

// =============================================================================
// CONVENIENCE PROVIDERS (Non-Riverpod)
// =============================================================================

/// Convenience provider for auth state manager authentication state
Provider<AuthState> authManagerCurrentStateProvider = Provider<AuthState>((
  ref,
) {
  final manager = ref.watch(authStateManagerProvider);
  return manager.currentAuthState;
});

/// Convenience provider for auth state manager current user
Provider<User?> authManagerCurrentUserProvider = Provider<User?>((ref) {
  final manager = ref.watch(authStateManagerProvider);
  return manager.currentUser;
});

/// Convenience provider for auth state manager authentication status
Provider<bool> authManagerIsAuthenticatedProvider = Provider<bool>((ref) {
  final manager = ref.watch(authStateManagerProvider);
  return manager.isAuthenticated;
});

/// Convenience provider for auth system readiness
Provider<bool> isAuthReadyProvider = Provider<bool>((ref) {
  final manager = ref.watch(authStateManagerProvider);
  return manager.isReady;
});
