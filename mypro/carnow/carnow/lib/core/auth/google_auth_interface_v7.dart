// Simple implementation without freezed to avoid conflicts

/// Enhanced Google Authentication Interface for v7.1.1
/// واجهة المصادقة المحسنة لـ Google Sign-In v7.1.1
/// 
/// This interface supports the new Google Sign-In v7.1.1 API changes while
/// maintaining compatibility with Forever Plan Architecture and UnifiedAuthSystem.
/// 
/// Key Features:
/// - Singleton pattern with initialize() requirement
/// - authenticate() method for user sign-in
/// - attemptLightweightAuthentication() for silent authentication
/// - Synchronous authentication token access
/// - Enhanced error handling with GoogleSignInException
abstract class IGoogleAuthServiceV7 {
  /// Initialize the Google Sign-In service
  /// تهيئة خدمة Google Sign-In
  /// 
  /// This MUST be called exactly once before using any other methods.
  /// Required in v7.1.1 - the service won't work without initialization.
  /// 
  /// [clientId] - The Google OAuth client ID for the platform
  /// [scopes] - List of OAuth scopes to request (default: ['email', 'profile'])
  Future<void> initialize({
    required String clientId,
    List<String> scopes = const ['email', 'profile'],
  });

  /// Authenticate user with Google
  /// مصادقة المستخدم مع Google
  /// 
  /// This method shows the Google sign-in UI and authenticates the user.
  /// Throws GoogleSignInException on failure instead of returning null.
  /// 
  /// [scopeHint] - Optional scopes to hint for this authentication
  /// Returns GoogleAuthResult with user info and tokens
  Future<GoogleAuthResult> authenticate({
    List<String>? scopeHint,
  });

  /// Attempt lightweight authentication
  /// محاولة المصادقة الصامتة
  /// 
  /// Attempts to authenticate without showing UI if user is already signed in.
  /// May return synchronously or asynchronously depending on platform.
  /// 
  /// Returns null if no user is signed in or authentication fails
  Future<GoogleAuthResult?> attemptLightweightAuthentication();

  /// Sign out the current user
  /// تسجيل خروج المستخدم الحالي
  /// 
  /// Signs out the user but keeps the account available for future sign-ins
  Future<void> signOut();

  /// Disconnect the user completely
  /// قطع الاتصال مع المستخدم بالكامل
  /// 
  /// Revokes all permissions and removes the account from the device
  Future<void> disconnect();

  /// Check if the service is properly configured and initialized
  /// التحقق من تكوين وتهيئة الخدمة
  bool get isConfigured;

  /// Check if the platform supports the authenticate() method
  /// التحقق من دعم النظام لطريقة authenticate()
  bool get supportsAuthenticate;

  /// Get current user information if available
  /// الحصول على معلومات المستخدم الحالي إن وجدت
  /// 
  /// Note: User state needs to be managed manually in your application.
  GoogleUserInfo? get currentUser;
}

/// Google Authentication Result
/// نتيجة مصادقة Google
abstract class GoogleAuthResult {
  const GoogleAuthResult();

  /// Successful authentication
  /// مصادقة ناجحة
  const factory GoogleAuthResult.success({
    required String idToken,
    required String accessToken,
    required GoogleUserInfo userInfo,
    DateTime? expiryDate,
  }) = GoogleAuthResultSuccess;

  /// Authentication was cancelled by user
  /// تم إلغاء المصادقة من قبل المستخدم
  const factory GoogleAuthResult.cancelled({
    required String reason,
  }) = GoogleAuthResultCancelled;

  /// Authentication failed with error
  /// فشلت المصادقة مع خطأ
  const factory GoogleAuthResult.failure({
    required String error,
    required String errorCode,
    String? details,
  }) = GoogleAuthResultFailure;

  /// Pattern matching for result handling
  /// مطابقة الأنماط لمعالجة النتيجة
  T when<T>({
    required T Function(String idToken, String accessToken, GoogleUserInfo userInfo, DateTime? expiryDate) success,
    required T Function(String reason) cancelled,
    required T Function(String error, String errorCode, String? details) failure,
  }) {
    if (this is GoogleAuthResultSuccess) {
      final result = this as GoogleAuthResultSuccess;
      return success(result.idToken, result.accessToken, result.userInfo, result.expiryDate);
    } else if (this is GoogleAuthResultCancelled) {
      final result = this as GoogleAuthResultCancelled;
      return cancelled(result.reason);
    } else if (this is GoogleAuthResultFailure) {
      final result = this as GoogleAuthResultFailure;
      return failure(result.error, result.errorCode, result.details);
    } else {
      throw StateError('Unknown GoogleAuthResult type');
    }
  }
}

/// Success result implementation
class GoogleAuthResultSuccess extends GoogleAuthResult {
  final String idToken;
  final String accessToken;
  final GoogleUserInfo userInfo;
  final DateTime? expiryDate;

  const GoogleAuthResultSuccess({
    required this.idToken,
    required this.accessToken,
    required this.userInfo,
    this.expiryDate,
  });
}

/// Cancelled result implementation
class GoogleAuthResultCancelled extends GoogleAuthResult {
  final String reason;

  const GoogleAuthResultCancelled({
    required this.reason,
  });
}

/// Failure result implementation
class GoogleAuthResultFailure extends GoogleAuthResult {
  final String error;
  final String errorCode;
  final String? details;

  const GoogleAuthResultFailure({
    required this.error,
    required this.errorCode,
    this.details,
  });
}

/// Google User Information
/// معلومات مستخدم Google
class GoogleUserInfo {
  final String id;
  final String email;
  final String displayName;
  final String? firstName;
  final String? lastName;
  final String? photoUrl;

  const GoogleUserInfo({
    required this.id,
    required this.email,
    required this.displayName,
    this.firstName,
    this.lastName,
    this.photoUrl,
  });

  factory GoogleUserInfo.fromJson(Map<String, dynamic> json) {
    return GoogleUserInfo(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      photoUrl: json['photoUrl'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'firstName': firstName,
      'lastName': lastName,
      'photoUrl': photoUrl,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GoogleUserInfo &&
        other.id == id &&
        other.email == email &&
        other.displayName == displayName &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.photoUrl == photoUrl;
  }

  @override
  int get hashCode {
    return Object.hash(id, email, displayName, firstName, lastName, photoUrl);
  }

  @override
  String toString() {
    return 'GoogleUserInfo(id: $id, email: $email, displayName: $displayName, firstName: $firstName, lastName: $lastName, photoUrl: $photoUrl)';
  }
}

/// Google Sign-In Exception Codes (v7.1.1)
/// رموز استثناءات Google Sign-In
enum GoogleSignInExceptionCode {
  /// User cancelled the sign-in process
  /// المستخدم ألغى عملية تسجيل الدخول
  canceled,
  
  /// Sign-in process was interrupted
  /// تم مقاطعة عملية تسجيل الدخول
  interrupted,
  
  /// Client configuration error
  /// خطأ في تكوين العميل
  clientConfigurationError,
  
  /// Provider configuration error
  /// خطأ في تكوين المزود
  providerConfigurationError,
  
  /// UI is unavailable
  /// الواجهة غير متاحة
  uiUnavailable,
  
  /// User mismatch error
  /// خطأ عدم تطابق المستخدم
  userMismatch,
  
  /// Unknown error occurred
  /// حدث خطأ غير معروف
  unknownError,
}

/// Enhanced Google Sign-In Exception for v7.1.1
/// استثناء Google Sign-In المحسن لـ v7.1.1
class GoogleSignInException implements Exception {
  final GoogleSignInExceptionCode code;
  final String description;
  final String? details;

  const GoogleSignInException({
    required this.code,
    required this.description,
    this.details,
  });

  @override
  String toString() {
    return 'GoogleSignInException(code: ${code.name}, description: $description, details: $details)';
  }

  /// Convert exception to user-friendly Arabic message
  /// تحويل الاستثناء إلى رسالة مفهومة بالعربية
  String toArabicMessage() {
    switch (code) {
      case GoogleSignInExceptionCode.canceled:
        return 'تم إلغاء تسجيل الدخول. يرجى المحاولة مرة أخرى إذا كنت تريد المتابعة.';
      case GoogleSignInExceptionCode.interrupted:
        return 'تم مقاطعة تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      case GoogleSignInExceptionCode.clientConfigurationError:
        return 'يوجد مشكلة في تكوين تسجيل الدخول بـ Google. يرجى التواصل مع الدعم.';
      case GoogleSignInExceptionCode.providerConfigurationError:
        return 'تسجيل الدخول بـ Google غير متاح حالياً. يرجى المحاولة لاحقاً أو التواصل مع الدعم.';
      case GoogleSignInExceptionCode.uiUnavailable:
        return 'تسجيل الدخول بـ Google غير متاح حالياً. يرجى المحاولة لاحقاً أو التواصل مع الدعم.';
      case GoogleSignInExceptionCode.userMismatch:
        return 'يوجد مشكلة مع حسابك. يرجى تسجيل الخروج والمحاولة مرة أخرى.';
      case GoogleSignInExceptionCode.unknownError:
        return 'حدث خطأ غير متوقع أثناء تسجيل الدخول بـ Google. يرجى المحاولة مرة أخرى.';
    }
  }
}


