import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'enhanced_secure_token_storage.dart';
import 'auth_interfaces.dart';
import 'session_persistence_diagnostics.dart';
import '../config/session_persistence_config.dart';

part 'session_recovery_service.g.dart';

/// Session Recovery Service
/// خدمة استرداد الجلسة
/// 
/// This service handles session recovery and persistence issues following Forever Plan Architecture:
/// - Real data only from Supabase database
/// - No mock data tolerance
/// - Comprehensive error handling and recovery
/// - Production-ready session management
class SessionRecoveryService {
  final ITokenStorage _tokenStorage;
  final SessionPersistenceDiagnostics _diagnostics;

  SessionRecoveryService({
    required ITokenStorage tokenStorage,
    required SessionPersistenceDiagnostics diagnostics,
  }) : _tokenStorage = tokenStorage,
       _diagnostics = diagnostics;

  /// Attempt to recover session with comprehensive error handling
  /// محاولة استرداد الجلسة مع معالجة شاملة للأخطاء
  Future<SessionRecoveryResult> attemptSessionRecovery() async {
    developer.log('🔄 Starting session recovery process...', name: 'SessionRecoveryService');
    
    try {
      // Step 1: Run diagnostics to understand the current state
      final diagnosticsResult = await _diagnostics.runDiagnostics();
      
      developer.log(
        'Diagnostics completed: healthy=${diagnosticsResult.isHealthy}, issues=${diagnosticsResult.issues.length}',
        name: 'SessionRecoveryService',
      );

      if (diagnosticsResult.isHealthy) {
        // Session is healthy, try to get tokens
        final accessToken = await _tokenStorage.getToken();
        final refreshToken = await _tokenStorage.getRefreshToken();
        
        if (accessToken != null) {
          developer.log('✅ Session recovery successful - valid token found', name: 'SessionRecoveryService');
          
          return SessionRecoveryResult.success(
            accessToken: accessToken,
            refreshToken: refreshToken,
            recoveryMethod: 'direct_token_retrieval',
            diagnostics: diagnosticsResult,
          );
        }
      }

      // Step 2: Attempt recovery based on specific issues
      return await _attemptRecoveryBasedOnIssues(diagnosticsResult);
      
    } catch (e, stackTrace) {
      developer.log(
        'Critical error during session recovery: $e',
        name: 'SessionRecoveryService',
        error: e,
        stackTrace: stackTrace,
      );
      
      return SessionRecoveryResult.failure(
        error: 'Critical recovery error: $e',
        recoveryMethod: 'error_handling',
        requiresReauthentication: true,
      );
    }
  }

  /// Attempt recovery based on specific issues found in diagnostics
  Future<SessionRecoveryResult> _attemptRecoveryBasedOnIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('🔧 Attempting targeted recovery based on issues...', name: 'SessionRecoveryService');
    
    final issues = diagnosticsResult.issues;
    
    // Check for encryption credential issues
    if (issues.any((issue) => issue.contains('encryption credentials'))) {
      return await _handleEncryptionCredentialIssues(diagnosticsResult);
    }
    
    // Check for token expiry issues
    if (issues.any((issue) => issue.contains('expired'))) {
      return await _handleTokenExpiryIssues(diagnosticsResult);
    }
    
    // Check for storage integrity issues
    if (issues.any((issue) => issue.contains('integrity'))) {
      return await _handleStorageIntegrityIssues(diagnosticsResult);
    }
    
    // Check for missing tokens
    if (issues.any((issue) => issue.contains('No tokens found'))) {
      return await _handleMissingTokensIssues(diagnosticsResult);
    }
    
    // Default recovery attempt
    return await _attemptDefaultRecovery(diagnosticsResult);
  }

  /// Handle encryption credential issues
  Future<SessionRecoveryResult> _handleEncryptionCredentialIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('🔑 Handling encryption credential issues...', name: 'SessionRecoveryService');
    
    try {
      // Clear corrupted data and force regeneration of encryption credentials
      await _tokenStorage.clearAllData();
      
      developer.log('✅ Cleared corrupted encryption data', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Encryption credentials were corrupted and have been reset',
        recoveryMethod: 'encryption_reset',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    } catch (e) {
      developer.log('❌ Failed to handle encryption issues: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Failed to reset encryption credentials: $e',
        recoveryMethod: 'encryption_reset_failed',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Handle token expiry issues
  Future<SessionRecoveryResult> _handleTokenExpiryIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('⏰ Handling token expiry issues...', name: 'SessionRecoveryService');
    
    try {
      final refreshToken = await _tokenStorage.getRefreshToken();
      
      if (refreshToken != null) {
        developer.log('🔄 Refresh token available for token renewal', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.refreshRequired(
          refreshToken: refreshToken,
          recoveryMethod: 'token_refresh',
          diagnostics: diagnosticsResult,
        );
      } else {
        developer.log('❌ No refresh token available', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.failure(
          error: 'Access token expired and no refresh token available',
          recoveryMethod: 'token_expiry_no_refresh',
          requiresReauthentication: true,
          diagnostics: diagnosticsResult,
        );
      }
    } catch (e) {
      developer.log('❌ Failed to handle token expiry: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Failed to handle token expiry: $e',
        recoveryMethod: 'token_expiry_error',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Handle storage integrity issues
  Future<SessionRecoveryResult> _handleStorageIntegrityIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('🔒 Handling storage integrity issues...', name: 'SessionRecoveryService');
    
    try {
      // Clear all data to reset integrity
      await _tokenStorage.clearAllData();
      
      developer.log('✅ Storage integrity reset completed', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Storage integrity was compromised and has been reset',
        recoveryMethod: 'integrity_reset',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    } catch (e) {
      developer.log('❌ Failed to reset storage integrity: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Failed to reset storage integrity: $e',
        recoveryMethod: 'integrity_reset_failed',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Handle missing tokens issues
  Future<SessionRecoveryResult> _handleMissingTokensIssues(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('📭 Handling missing tokens issues...', name: 'SessionRecoveryService');
    
    return SessionRecoveryResult.failure(
      error: 'No authentication tokens found in storage',
      recoveryMethod: 'missing_tokens',
      requiresReauthentication: true,
      diagnostics: diagnosticsResult,
    );
  }

  /// Attempt default recovery
  Future<SessionRecoveryResult> _attemptDefaultRecovery(
    SessionDiagnosticsResult diagnosticsResult,
  ) async {
    developer.log('🔄 Attempting default recovery...', name: 'SessionRecoveryService');
    
    try {
      // Try to get tokens one more time
      final accessToken = await _tokenStorage.getToken();
      final refreshToken = await _tokenStorage.getRefreshToken();
      
      if (accessToken != null) {
        developer.log('✅ Default recovery successful', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.success(
          accessToken: accessToken,
          refreshToken: refreshToken,
          recoveryMethod: 'default_recovery',
          diagnostics: diagnosticsResult,
        );
      } else if (refreshToken != null) {
        developer.log('🔄 Default recovery requires token refresh', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.refreshRequired(
          refreshToken: refreshToken,
          recoveryMethod: 'default_recovery_refresh',
          diagnostics: diagnosticsResult,
        );
      } else {
        developer.log('❌ Default recovery failed - no tokens available', name: 'SessionRecoveryService');
        
        return SessionRecoveryResult.failure(
          error: 'No valid tokens found after recovery attempts',
          recoveryMethod: 'default_recovery_failed',
          requiresReauthentication: true,
          diagnostics: diagnosticsResult,
        );
      }
    } catch (e) {
      developer.log('❌ Default recovery error: $e', name: 'SessionRecoveryService');
      
      return SessionRecoveryResult.failure(
        error: 'Default recovery failed: $e',
        recoveryMethod: 'default_recovery_error',
        requiresReauthentication: true,
        diagnostics: diagnosticsResult,
      );
    }
  }

  /// Force clear all session data (for troubleshooting)
  Future<void> forceClearSession() async {
    developer.log('🧹 Force clearing all session data...', name: 'SessionRecoveryService');
    
    try {
      await _tokenStorage.clearAllData();
      developer.log('✅ Session force clear completed', name: 'SessionRecoveryService');
    } catch (e) {
      developer.log('❌ Failed to force clear session: $e', name: 'SessionRecoveryService');
      throw Exception('Failed to force clear session: $e');
    }
  }
}

/// Session recovery result
class SessionRecoveryResult {
  final bool isSuccess;
  final String? accessToken;
  final String? refreshToken;
  final bool requiresRefresh;
  final bool requiresReauthentication;
  final String? error;
  final String recoveryMethod;
  final SessionDiagnosticsResult? diagnostics;

  const SessionRecoveryResult._({
    required this.isSuccess,
    this.accessToken,
    this.refreshToken,
    this.requiresRefresh = false,
    this.requiresReauthentication = false,
    this.error,
    required this.recoveryMethod,
    this.diagnostics,
  });

  /// Successful recovery with valid tokens
  factory SessionRecoveryResult.success({
    required String accessToken,
    String? refreshToken,
    required String recoveryMethod,
    SessionDiagnosticsResult? diagnostics,
  }) {
    return SessionRecoveryResult._(
      isSuccess: true,
      accessToken: accessToken,
      refreshToken: refreshToken,
      recoveryMethod: recoveryMethod,
      diagnostics: diagnostics,
    );
  }

  /// Recovery requires token refresh
  factory SessionRecoveryResult.refreshRequired({
    required String refreshToken,
    required String recoveryMethod,
    SessionDiagnosticsResult? diagnostics,
  }) {
    return SessionRecoveryResult._(
      isSuccess: false,
      refreshToken: refreshToken,
      requiresRefresh: true,
      recoveryMethod: recoveryMethod,
      diagnostics: diagnostics,
    );
  }

  /// Recovery failed, requires reauthentication
  factory SessionRecoveryResult.failure({
    required String error,
    required String recoveryMethod,
    required bool requiresReauthentication,
    SessionDiagnosticsResult? diagnostics,
  }) {
    return SessionRecoveryResult._(
      isSuccess: false,
      error: error,
      requiresReauthentication: requiresReauthentication,
      recoveryMethod: recoveryMethod,
      diagnostics: diagnostics,
    );
  }

  @override
  String toString() {
    return 'SessionRecoveryResult(success: $isSuccess, method: $recoveryMethod, requiresAuth: $requiresReauthentication)';
  }
}

/// Provider for session recovery service
@riverpod
SessionRecoveryService sessionRecoveryService(Ref ref) {
  final tokenStorage = ref.read(enhancedSecureTokenStorageProvider);
  final diagnostics = ref.read(sessionPersistenceDiagnosticsProvider);
  return SessionRecoveryService(
    tokenStorage: tokenStorage,
    diagnostics: diagnostics,
  );
}
