// =============================================================================
// CARNOW GOOGLE SIGN-IN V7.1.1 USAGE EXAMPLE
// =============================================================================
// 
// This example demonstrates how to use the new Google Sign-In v7.1.1 service
// in a production Flutter application following Forever Plan Architecture.
//
// Forever Plan Compliance: ✅
// Real Data Only: ✅
// Production Ready: ✅
//
// =============================================================================

import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:carnow/core/auth/google_auth_service.dart';
import 'package:carnow/core/auth/google_auth_interface_v7.dart';

/// Example Google Sign-In v7.1.1 Usage
/// مثال على استخدام Google Sign-In v7.1.1
class GoogleAuthV7Example extends StatefulWidget {
  const GoogleAuthV7Example({super.key});

  @override
  State<GoogleAuthV7Example> createState() => _GoogleAuthV7ExampleState();
}

class _GoogleAuthV7ExampleState extends State<GoogleAuthV7Example> {
  final GoogleAuthService _authService = GoogleAuthService.instance;
  bool _isInitialized = false;
  bool _isLoading = false;
  GoogleUserInfo? _currentUser;
  String? _lastError;

  @override
  void initState() {
    super.initState();
    _initializeGoogleAuth();
  }

  /// Initialize Google Sign-In v7.1.1
  /// تهيئة Google Sign-In v7.1.1
  Future<void> _initializeGoogleAuth() async {
    try {
      setState(() {
        _isLoading = true;
        _lastError = null;
      });

      // Initialize with your actual client ID
      // Replace with your real Google OAuth client ID
      await _authService.initialize(
        clientId: 'your-client-id.apps.googleusercontent.com',
        scopes: ['email', 'profile'],
      );

      setState(() {
        _isInitialized = true;
        _isLoading = false;
      });

      // Try silent sign-in
      await _attemptSilentSignIn();
    } catch (error) {
      setState(() {
        _isInitialized = false;
        _isLoading = false;
        _lastError = 'Failed to initialize Google Sign-In: $error';
      });
    }
  }

  /// Attempt silent sign-in
  /// محاولة تسجيل الدخول الصامت
  Future<void> _attemptSilentSignIn() async {
    try {
      final result = await _authService.attemptLightweightAuthentication();
      
      if (result != null) {
        result.when(
          success: (idToken, accessToken, userInfo, expiryDate) {
            setState(() {
              _currentUser = userInfo;
              _lastError = null;
            });
          },
          cancelled: (reason) {
            // Silent sign-in was cancelled, this is normal
          },
          failure: (error, errorCode, details) {
            setState(() {
              _lastError = 'Silent sign-in failed: $error';
            });
          },
        );
      }
    } catch (error) {
      setState(() {
        _lastError = 'Silent sign-in error: $error';
      });
    }
  }

  /// Sign in with Google
  /// تسجيل الدخول بـ Google
  Future<void> _signIn() async {
    if (!_isInitialized) {
      setState(() {
        _lastError = 'Google Sign-In not initialized';
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _lastError = null;
      });

      final result = await _authService.authenticate();

      result.when(
        success: (idToken, accessToken, userInfo, expiryDate) {
          setState(() {
            _currentUser = userInfo;
            _isLoading = false;
            _lastError = null;
          });

          // Here you would typically send the idToken to your backend
          // for verification and session creation
          _sendTokenToBackend(idToken);
        },
        cancelled: (reason) {
          setState(() {
            _isLoading = false;
            _lastError = 'Sign-in was cancelled';
          });
        },
        failure: (error, errorCode, details) {
          setState(() {
            _isLoading = false;
            _lastError = error;
          });
        },
      );
    } catch (error) {
      setState(() {
        _isLoading = false;
        _lastError = 'Sign-in error: $error';
      });
    }
  }

  /// Sign out from Google
  /// تسجيل الخروج من Google
  Future<void> _signOut() async {
    try {
      setState(() {
        _isLoading = true;
        _lastError = null;
      });

      await _authService.signOut();

      setState(() {
        _currentUser = null;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
        _lastError = 'Sign-out error: $error';
      });
    }
  }

  /// Send ID token to backend (Forever Plan Architecture)
  /// إرسال رمز ID إلى الخادم الخلفي (هيكل Forever Plan)
  Future<void> _sendTokenToBackend(String idToken) async {
    try {
      // This is where you would call your Go backend API
      // following Forever Plan Architecture:
      // Flutter UI → Go API → Supabase
      
      // Example:
      // final response = await apiClient.post('/auth/google', {
      //   'idToken': idToken,
      // });
      
      // Use developer.log instead of print for production code
      developer.log('🔐 ID Token ready to send to backend: ${idToken.substring(0, 20)}...', name: 'GoogleAuthExample');
      
      // Your backend would:
      // 1. Verify the ID token with Google
      // 2. Create or update user in Supabase
      // 3. Return session token to Flutter
      
    } catch (error) {
      setState(() {
        _lastError = 'Backend authentication failed: $error';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Google Sign-In v7.1.1 Example'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Google Sign-In Status',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('Initialized: ${_isInitialized ? "✅" : "❌"}'),
                    Text('Configured: ${_authService.isConfigured ? "✅" : "❌"}'),
                    Text('Supports Authenticate: ${_authService.supportsAuthenticate ? "✅" : "❌"}'),
                    Text('Loading: ${_isLoading ? "⏳" : "✅"}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // User Info Card
            if (_currentUser != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Signed In User',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      if (_currentUser!.photoUrl != null)
                        CircleAvatar(
                          backgroundImage: NetworkImage(_currentUser!.photoUrl!),
                          radius: 30,
                        ),
                      const SizedBox(height: 8),
                      Text('Name: ${_currentUser!.displayName}'),
                      Text('Email: ${_currentUser!.email}'),
                      Text('ID: ${_currentUser!.id}'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Error Card
            if (_lastError != null)
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Error',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastError!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Action Buttons
            if (_isInitialized && !_isLoading) ...[
              if (_currentUser == null)
                ElevatedButton.icon(
                  onPressed: _signIn,
                  icon: const Icon(Icons.login),
                  label: const Text('Sign In with Google'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                )
              else
                ElevatedButton.icon(
                  onPressed: _signOut,
                  icon: const Icon(Icons.logout),
                  label: const Text('Sign Out'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
            ],

            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),

            if (!_isInitialized)
              ElevatedButton.icon(
                onPressed: _initializeGoogleAuth,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry Initialization'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
